"""
航线分析工具

分析指定地点之间的航班运营数据和市场情况
"""

import logging
from datetime import datetime
from typing import Dict, Any
from typing_extensions import Annotated
from pydantic import Field

from .server import mcp
from ..schemas import FLIGHT_DATA_ANALYSIS_SCHEMA
from ..utils import handle_api_errors
from ..api_client import make_flight_request
from ..analyzers import (
    FlightAnalyzer,
    TemporalAnalyzer,
    PunctualityAnalyzer,
    DurationAnalyzer,
    FlightNumberAnalyzer,
    RouteAnalyzer
)

logger = logging.getLogger(__name__)


@mcp.tool(output_schema=FLIGHT_DATA_ANALYSIS_SCHEMA)
@handle_api_errors
async def analyze_flight_between_locations(
    from_location: Annotated[str, Field(description="出发地点名称 - 支持中文城市名或国家名，例如 '北京', '阿联酋'")],
    to_location: Annotated[str, Field(description="到达地点名称 - 支持中文城市名或国家名，例如 '上海', '美国'")],
    date_from: Annotated[str, Field(description="查询开始日期 (YYYY-MM-DD 格式) - 建议查询范围不超过30天以获得最佳性能")],
    date_to: Annotated[str, Field(description="查询结束日期 (YYYY-MM-DD 格式) - 必须晚于或等于开始日期")],
    is_from_country: Annotated[bool, Field(description="出发地点是否为国家", default=False)],
    is_to_country: Annotated[bool, Field(description="到达地点是否为国家", default=False)],
    detail_level: Annotated[str, Field(description="分析详细程度: 'summary'=基础统计 | 'detailed'=深度分析(含市场份额、准点率、时间模式等)", default="summary")]
) -> Dict[str, Any]:
    """
    全面分析指定地点之间（城市到城市、城市到国家、国家到城市）的航班运营数据和市场情况。
    
    该工具通过查询实时航班数据API，对特定航线进行深度数据分析，支持地理信息情报（GEOINT）应用。
    自动处理API分页，确保获取完整数据集进行分析。
    
    🔍 支持两种分析深度：
    
    📊 SUMMARY模式 - 基础运营统计：
    • 航班运营概况：总航班数、运营航空公司数量、机场分布
    • 机队分析：飞机型号分布、平均飞行时长
    • 航班状态：准点、延误、取消等状态分布
    • 市场参与者：主要航空公司及其航班数量排名
    
    📈 DETAILED模式 - 深度市场与运营分析：
    • 时间模式分析：每日/每小时航班分布、最繁忙时段识别、日均运营量
    • 准点率分析：准点/延误航班统计、平均延误时长、准点率百分比
    • 市场份额分析：各航空公司市场占有率、主导企业识别
    • 飞行时长统计：时长分布、分位数分析、异常值检测
    • 效率指标：航班状态分布、取消率、航空公司绩效排名
    • 重要航班识别：高频航班班次分析、各班次准点率、出发时间模式
    
    💡 适用场景：
    • 航线市场研究与竞争分析
    • 航空运输效率评估
    • 机场运营时间模式分析
    • 航空公司绩效比较
    • 航线运营密度评估
    • 地理空间交通流量分析
    
    📋 数据来源：实时航班数据API，包含起降时间、航空公司、机场、飞机型号等完整信息
    """
    try:
        # 根据输入判断查询类型和参数
        if is_from_country and not is_to_country:
            origin_type = 'country'
            destination_type = 'city'
        elif not is_from_country and is_to_country:
            origin_type = 'city'
            destination_type = 'country'
        elif not is_from_country and not is_to_country:
            origin_type = 'city'
            destination_type = 'city'
        else:
            return {
                "error": "查询类型无效：不能同时从国家出发并到达国家。",
                "analysis_date": datetime.now().isoformat()
            }

        # 调用 API 获取数据
        api_response = await make_flight_request(
            origin_location=from_location,
            origin_type=origin_type,
            destination_location=to_location,
            destination_type=destination_type,
            date_from=date_from,
            date_to=date_to
        )
        
        flights = api_response.get("results", [])
        
        if not flights:
            return {
                "route": f"{from_location} → {to_location}",
                "date_range": f"{date_from} to {date_to}",
                "detail_level": detail_level,
                "summary": "该航线在指定日期范围内未找到航班数据",
                "analysis_date": datetime.now().isoformat()
            }
        
        # 使用分析器提取基本统计信息
        basic_stats = FlightAnalyzer.extract_basic_stats(flights)
        
        result = {
            "route": f"{from_location} → {to_location}",
            "date_range": f"{date_from} to {date_to}",
            "detail_level": detail_level,
            "summary": {
                "total_flights": basic_stats["total_flights"],
                "unique_airlines": basic_stats["unique_airlines"],
                "unique_departure_airports": basic_stats["unique_departure_airports"],
                "unique_arrival_airports": basic_stats["unique_arrival_airports"],
                "average_flight_duration_minutes": basic_stats["average_flight_duration_minutes"]
            },
            "airlines": dict(basic_stats["airlines"].most_common(5)),
            "airports": {
                "departure": dict(basic_stats["airports_from"].most_common(5)),
                "arrival": dict(basic_stats["airports_to"].most_common(5))
            },
            "aircraft_models": dict(basic_stats["aircraft_models"].most_common(5)),
            "flight_statuses": dict(basic_stats["flight_statuses"]),
            "analysis_date": datetime.now().isoformat()
        }
        
        # 如果是详细模式，添加额外的分析
        if detail_level == "detailed":
            temporal_data = TemporalAnalyzer.analyze_patterns(flights)
            punctuality_data = PunctualityAnalyzer.calculate_metrics(flights)
            
            result["temporal_analysis"] = {
                "daily_distribution": dict(sorted(temporal_data["daily_flights"].items())),
                "hourly_distribution": dict(sorted(temporal_data["hourly_departures"].items())),
                "busiest_date": {
                    "date": temporal_data["busiest_date"][0] if temporal_data["busiest_date"] else None,
                    "flights": temporal_data["busiest_date"][1] if temporal_data["busiest_date"] else None
                },
                "busiest_hour": {
                    "hour": temporal_data["busiest_hour"][0] if temporal_data["busiest_hour"] else None,
                    "flights": temporal_data["busiest_hour"][1] if temporal_data["busiest_hour"] else None
                },
                "daily_average": temporal_data["daily_average"]
            }
            
            result["punctuality_analysis"] = {
                "on_time_flights": punctuality_data["on_time_flights"],
                "delayed_flights": punctuality_data["delayed_flights"],
                "punctuality_rate_percent": punctuality_data["punctuality_rate_percent"],
                "average_delay_minutes": punctuality_data["average_delay_minutes"]
            }
            
            # 市场分析
            total_flights = len(flights)
            from ..utils import safe_divide
            airline_percentages = {
                airline: round(safe_divide(count, total_flights) * 100, 2)
                for airline, count in basic_stats["airlines"].items()
            }
            
            result["market_analysis"] = {
                "airline_market_share": airline_percentages,
                "dominant_airline": basic_stats["airlines"].most_common(1)[0] if basic_stats["airlines"] else None
            }
            
            # 添加增强功能
            result["duration_analysis"] = DurationAnalyzer.analyze_patterns(flights)
            result["efficiency_metrics"] = RouteAnalyzer.analyze_efficiency_metrics(flights)
            result["important_flights"] = FlightNumberAnalyzer.analyze_important_numbers(flights)
        
        return result
        
    except Exception as e:
        logger.error(f"分析 {from_location} 到 {to_location} 航班时发生错误: {e}", exc_info=True)
        return {
            "error": "处理请求时发生错误",
            "details": str(e),
            "route": f"{from_location} → {to_location}",
            "date_range": f"{date_from} to {date_to}",
            "detail_level": detail_level,
            "analysis_date": datetime.now().isoformat()
        }