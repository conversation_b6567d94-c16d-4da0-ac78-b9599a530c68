"""
通用辅助函数模块

存放小而通用的辅助函数和装饰器
"""

import functools
import logging
from datetime import datetime
from typing import Optional, Callable, Any

logger = logging.getLogger(__name__)


def clean_airport_name(airport_name: str) -> str:
    """
    清理机场名称，去除常见的机场后缀以提高搜索准确性
    
    Args:
        airport_name: 原始机场名称
        
    Returns:
        清理后的机场名称
    """
    # 定义需要去除的后缀（中文和英文）
    suffixes_to_remove = [
        "国际机场", "机场", "航空港", "空港",
        "International Airport", "Airport", "Intl Airport", 
        "Regional Airport", "Municipal Airport", "Field"
    ]
    
    cleaned_name = airport_name.strip()
    
    # 去除后缀
    for suffix in suffixes_to_remove:
        if cleaned_name.endswith(suffix):
            cleaned_name = cleaned_name[:-len(suffix)].strip()
            break  # 只去除第一个匹配的后缀
    
    # 如果清理后名称太短，使用原始名称
    if len(cleaned_name) < 2:
        cleaned_name = airport_name
        
    return cleaned_name


def calculate_flight_duration(departure_time: str, arrival_time: str) -> Optional[int]:
    """
    计算航班飞行时长（分钟）
    
    Args:
        departure_time: 起飞时间字符串
        arrival_time: 到达时间字符串
        
    Returns:
        飞行时长（分钟），如果计算失败返回None
    """
    try:
        # 尝试解析时间格式
        time_formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%dT%H:%M:%S",
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%dT%H:%M:%S.%fZ"
        ]
        
        departure_dt = None
        arrival_dt = None
        
        for fmt in time_formats:
            try:
                departure_dt = datetime.strptime(departure_time, fmt)
                break
            except ValueError:
                continue
                
        for fmt in time_formats:
            try:
                arrival_dt = datetime.strptime(arrival_time, fmt)
                break
            except ValueError:
                continue
        
        if departure_dt and arrival_dt:
            duration = arrival_dt - departure_dt
            return int(duration.total_seconds() / 60)  # 转换为分钟
        
        return None
        
    except Exception as e:
        logger.warning(f"计算飞行时长失败: {e}")
        return None


def handle_api_errors(func: Callable) -> Callable:
    """
    统一API错误处理装饰器
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"API调用失败 in {func.__name__}: {e}")
            # 根据错误类型返回适当的错误信息
            if "timeout" in str(e).lower():
                return {"error": "请求超时，请稍后重试"}
            elif "connection" in str(e).lower():
                return {"error": "网络连接失败，请检查网络状态"}
            else:
                return {"error": f"API调用失败: {str(e)}"}
    
    return wrapper


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 除零时的默认值
        
    Returns:
        除法结果或默认值
    """
    try:
        return numerator / denominator if denominator != 0 else default
    except (TypeError, ValueError):
        return default


def format_percentage(value: float, decimal_places: int = 1) -> str:
    """
    格式化百分比显示
    
    Args:
        value: 数值（0-1之间）
        decimal_places: 小数位数
        
    Returns:
        格式化的百分比字符串
    """
    try:
        return f"{value * 100:.{decimal_places}f}%"
    except (TypeError, ValueError):
        return "N/A"