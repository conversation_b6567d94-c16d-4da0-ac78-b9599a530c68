# GEOINT Flights MCP Server - 航班地理情报分析平台

专业的MCP服务器，为地理情报（GEOINT）应用提供先进的航班数据分析功能。该服务器通过实时航班数据API，深度分析航线运营、市场动态、运营效率和时间模式，提供全面的航空运输情报洞察。

## 核心功能 (Core Features)

🛫 **航线运营分析** - Route Operation Analysis  
🏢 **市场竞争情报** - Market Competition Intelligence  
⏰ **时间模式识别** - Temporal Pattern Recognition  
📊 **准点率统计** - Punctuality Statistics  
✈️ **机队配置分析** - Fleet Configuration Analysis  
🎯 **关键航班识别** - Critical Flight Identification  
🌐 **城市矩阵分析** - Cities Matrix Analysis (新增)

## 可用工具 (Available Tools)

### 1. analyze_flight_between_locations
分析指定地点之间（城市到城市、城市到国家、国家到城市）的航班运营数据和市场情况。

**参数：**
- `from_location`: 出发地点名称
- `to_location`: 到达地点名称  
- `date_from`: 查询开始日期 (YYYY-MM-DD)
- `date_to`: 查询结束日期 (YYYY-MM-DD)
- `is_from_country`: 出发地点是否为国家 (默认: false)
- `is_to_country`: 到达地点是否为国家 (默认: false)
- `detail_level`: 分析详细程度 ('summary' 或 'detailed')

### 2. analyze_flight_location
分析从单个机场、城市或国家出发或到达的航班运营情况、航线网络和市场格局。

**参数：**
- `location`: 地点名称（支持机场名称、代码、城市名、国家名）
- `location_type`: 地点类型 ('city', 'country', 'airport')
- `date_from`: 查询开始日期 (YYYY-MM-DD)
- `date_to`: 查询结束日期 (YYYY-MM-DD)
- `direction`: 分析方向 ('from' 或 'to')
- `is_international`: 是否只看国际航班 (可选)
- `detail_level`: 分析详细程度 ('summary' 或 'detailed')

### 3. analyze_flight_matrix_cities (新增)
分析多个城市间的航班矩阵情况，获取城市间航班连接的全面统计信息。

**参数：**
- `cities`: 城市列表，用逗号分隔，例如 '北京,上海,广州,深圳'
- `date_from`: 查询开始日期 (YYYY-MM-DD，默认: 2024-01-01)
- `date_to`: 查询结束日期 (YYYY-MM-DD，默认: 2024-01-31)

**分析内容：**
- 📊 航班连接矩阵：城市对航班数量、机场覆盖、双向连接分析
- 📈 网络统计摘要：参与城市、航线总数、航班总量、连接密度
- 🎯 热门航线识别：最繁忙航线、航班分布、机场利用率
- 🏙️ 城市活跃度：各城市的出发/到达航班统计
- 📊 航线分布：高频/低频航线分类统计

## 分析深度 (Analysis Levels)

### Summary 模式 - 基础运营统计
- 航班运营概况：总航班数、运营航空公司数量、机场分布
- 机队分析：飞机型号分布、平均飞行时长
- 航班状态：准点、延误、取消等状态分布
- 市场参与者：主要航空公司及其航班数量排名

### Detailed 模式 - 深度市场与运营分析
- 时间模式分析：每日/每小时航班分布、最繁忙时段识别
- 准点率分析：准点/延误航班统计、平均延误时长
- 市场份额分析：各航空公司市场占有率、主导企业识别
- 飞行时长统计：时长分布、分位数分析、异常值检测
- 效率指标：航班状态分布、取消率、航空公司绩效排名
- 重要航班识别：高频航班班次分析、各班次准点率

## 适用领域 (Application Areas)

• **航空市场研究** (Aviation Market Research)  
• **运输地理分析** (Transportation Geography)  
• **竞争情报收集** (Competitive Intelligence)  
• **运营效率评估** (Operational Efficiency Assessment)  
• **航线网络规划** (Route Network Planning)  
• **区域连接度分析** (Regional Connectivity Analysis)

## 机场支持增强

### 智能机场名称解析
- **机场名称**：支持中文和英文机场名称模糊查询
  - 中文：'首都国际机场', '北京大兴国际机场'
  - 英文：'Beijing Capital', 'Shanghai Pudong International Airport'
- **机场代码**：支持ICAO代码（如 'ZBAA'）和IATA代码（如 'PEK'）
- **自动解析**：机场名称会自动通过搜索API解析为对应的机场代码

### 智能名称清理
- 自动去除常见后缀：'国际机场', '机场', 'International Airport', 'Airport'
- 提高搜索准确性：'北京大兴国际机场' → '北京大兴' → 找到正确的ZBAD机场
- 备选策略：如果清理后的名称未找到，自动尝试原始名称

## 使用示例

### 城市间航班分析
```python
# 分析北京到上海的航班情况
result = await analyze_flight_between_locations(
    from_location="北京",
    to_location="上海", 
    date_from="2024-01-01",
    date_to="2024-01-07",
    detail_level="detailed"
)
```

### 机场运营分析
```python
# 分析首都国际机场的出发航班
result = await analyze_flight_location(
    location="首都国际机场",
    location_type="airport",
    date_from="2024-01-01", 
    date_to="2024-01-02",
    direction="from",
    detail_level="detailed"
)
```

### 城市矩阵分析 (新功能)
```python
# 分析多个城市间的航班连接矩阵
result = await analyze_flight_matrix_cities(
    cities="北京,上海,广州,深圳",
    date_from="2024-01-01",
    date_to="2024-01-31"
)
```

## 技术架构

- **框架**: FastMCP + FastAPI
- **异步处理**: httpx + asyncio
- **数据分析**: 统计学分析、时间序列分析、网络分析
- **API集成**: 实时航班数据API、机场搜索API
- **错误处理**: 完善的异常处理和日志记录

## 部署说明

### 环境要求
- Python 3.8+
- 依赖包：见 requirements.txt

### 运行服务
```bash
python mcp_server.py
```

服务将在 `http://0.0.0.0:8005` 启动，提供以下端点：
- SSE: `/mcp/sse`
- Messages: `/mcp/messages/`

## 数据来源

- **航班数据API**: `http://127.0.0.1:8000/api/flights/`
- **机场搜索API**: `http://127.0.0.1:8000/api/search/locations/`
- **城市矩阵API**: `http://127.0.0.1:8000/api/flights/matrix/cities/` (新增)

## 更新日志

### v1.2.0 (最新)
- ✨ 新增 `analyze_flight_matrix_cities` 工具
- 🌐 支持多城市间航班矩阵分析
- 📊 增强城市连接度和网络密度分析
- 🎯 添加航线分布统计功能

### v1.1.0
- ✨ 增强机场名称搜索功能
- 🔍 智能机场名称清理和解析
- 🛫 支持中英文机场名称模糊查询
- 📍 自动机场代码转换

### v1.0.0
- 🚀 初始版本发布
- 📊 基础航班分析功能
- 🏢 市场竞争情报分析
- ⏰ 时间模式识别功能